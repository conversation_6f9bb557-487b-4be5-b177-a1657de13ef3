#!/usr/bin/env python3
"""
测试真实前端应用的History功能
通过模拟浏览器行为来验证修复效果
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_frontend_session_persistence():
    """测试前端session持久性"""
    print("=== 测试前端Session持久性 ===")
    
    # 模拟前端localStorage的行为
    session_key = 'temp_email_session_id'
    
    # 模拟第一次访问（生成新session）
    session_id_1 = f"session_{int(time.time() * 1000)}_{''.join(__import__('random').choices('abcdefghijklmnopqrstuvwxyz0123456789', k=9))}"
    print(f"1. 模拟第一次访问，生成session: {session_id_1}")
    
    # 生成第一个邮箱
    email1 = generate_email_with_session(session_id_1, "first-visit")
    print(f"2. 生成第一个邮箱: {email1}")
    
    # 模拟页面刷新（session应该保持不变，因为使用localStorage）
    session_id_2 = session_id_1  # localStorage会保持相同的session
    print(f"3. 模拟页面刷新，session保持: {session_id_2}")
    
    # 生成第二个邮箱
    email2 = generate_email_with_session(session_id_2, "after-refresh")
    print(f"4. 生成第二个邮箱: {email2}")
    
    # 获取历史记录
    history = get_history_for_session(session_id_2)
    print(f"5. 获取历史记录: {len(history)} 个邮箱")
    
    # 验证结果
    expected_emails = [email1, email2]
    actual_emails = [item.get('email_address') for item in history]
    
    success = all(email in actual_emails for email in expected_emails)
    print(f"6. 验证结果: {'成功' if success else '失败'}")
    
    if success:
        print("✅ 前端session持久性测试通过")
        print("✅ 页面刷新后历史记录保持完整")
    else:
        print("❌ 前端session持久性测试失败")
        print(f"期望邮箱: {expected_emails}")
        print(f"实际邮箱: {actual_emails}")
    
    return success

def test_history_display_with_deleted_emails():
    """测试历史记录中已删除邮箱的显示"""
    print("\n=== 测试已删除邮箱的历史记录显示 ===")
    
    session_id = f"test_deleted_{int(time.time())}"
    
    # 1. 生成邮箱
    email = generate_email_with_session(session_id, "will-be-deleted")
    print(f"1. 生成邮箱: {email}")
    
    # 2. 获取初始历史记录
    history_before = get_history_for_session(session_id)
    print(f"2. 删除前历史记录: {len(history_before)} 个邮箱")
    
    # 3. 删除邮箱
    delete_result = delete_email(email)
    print(f"3. 删除邮箱: {'成功' if delete_result else '失败'}")
    
    # 4. 获取删除后的历史记录
    history_after = get_history_for_session(session_id)
    print(f"4. 删除后历史记录: {len(history_after)} 个邮箱")
    
    # 5. 验证已删除邮箱的显示状态
    if history_after:
        deleted_item = history_after[0]  # 应该是刚删除的邮箱
        is_marked_deleted = not deleted_item.get('exists_in_db', True)
        print(f"5. 已删除邮箱标记: {'正确' if is_marked_deleted else '错误'}")
        print(f"   邮箱: {deleted_item.get('email_address')}")
        print(f"   exists_in_db: {deleted_item.get('exists_in_db')}")
        
        if is_marked_deleted:
            print("✅ 已删除邮箱正确标记，应显示删除线")
        else:
            print("❌ 已删除邮箱标记错误")
    
    return len(history_after) > 0 and not history_after[0].get('exists_in_db', True)

def generate_email_with_session(session_id, prefix):
    """使用指定session生成邮箱"""
    url = f"{BASE_URL}/api/generate-address"
    data = {
        "session_id": session_id,
        "custom_prefix": prefix
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 201:
            result = response.json()
            if result.get("success"):
                return result.get("data", {}).get("address")
    except Exception as e:
        print(f"生成邮箱异常: {e}")
    
    return None

def get_history_for_session(session_id):
    """获取指定session的历史记录"""
    url = f"{BASE_URL}/api/email-history"
    params = {"session_id": session_id}
    
    try:
        response = requests.get(url, params=params)
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                return result.get("data", {}).get("history", [])
    except Exception as e:
        print(f"获取历史记录异常: {e}")
    
    return []

def delete_email(email_address):
    """删除邮箱"""
    url = f"{BASE_URL}/api/delete-email"
    data = {"address": email_address}
    
    try:
        response = requests.delete(url, json=data)
        if response.status_code == 200:
            result = response.json()
            return result.get("success", False)
    except Exception as e:
        print(f"删除邮箱异常: {e}")
    
    return False

def main():
    print("开始测试真实前端应用的History功能...")
    
    # 测试1: Session持久性
    test1_passed = test_frontend_session_persistence()
    
    # 测试2: 已删除邮箱的显示
    test2_passed = test_history_display_with_deleted_emails()
    
    # 总结
    print(f"\n=== 测试总结 ===")
    print(f"Session持久性测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"已删除邮箱显示测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！History功能修复成功！")
        print("✅ 用户现在可以看到所有生成的邮箱历史记录")
        print("✅ 已删除或过期的邮箱会正确标记并显示删除线")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
