#!/usr/bin/env python3
"""
测试History功能修复的脚本
验证不同session_id的邮箱历史记录是否能正确显示
"""

import requests
import json
import sqlite3
from datetime import datetime, timezone, timedelta

# 配置
BASE_URL = "http://127.0.0.1:5000"
DB_PATH = "database/tempmail.db"

def test_get_history_for_session(session_id):
    """测试获取指定session的历史记录"""
    print(f"\n=== 测试获取session {session_id} 的历史记录 ===")
    
    url = f"{BASE_URL}/api/email-history"
    params = {"session_id": session_id}
    
    try:
        response = requests.get(url, params=params)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                history = data.get("data", {}).get("history", [])
                print(f"找到 {len(history)} 个历史邮箱:")
                for i, item in enumerate(history, 1):
                    status = "活跃" if item.get("is_active") else "非活跃"
                    expired = "已过期" if item.get("is_expired") else "未过期"
                    exists = "存在" if item.get("exists_in_db") else "已删除"
                    print(f"  {i}. {item.get('email_address')} - {status}, {expired}, {exists}")
            else:
                print(f"API返回错误: {data.get('error')}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

def get_all_sessions_from_db():
    """从数据库获取所有session_id"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT session_id FROM email_history ORDER BY session_id")
        sessions = [row[0] for row in cursor.fetchall()]
        conn.close()
        return sessions
    except Exception as e:
        print(f"数据库查询失败: {e}")
        return []

def test_generate_email_with_session(session_id, custom_prefix=None):
    """测试使用指定session_id生成邮箱"""
    print(f"\n=== 测试使用session {session_id} 生成邮箱 ===")
    
    url = f"{BASE_URL}/api/generate-address"
    data = {"session_id": session_id}
    if custom_prefix:
        data["custom_prefix"] = custom_prefix
    
    try:
        response = requests.post(url, json=data)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            if result.get("success"):
                email_data = result.get("data", {})
                print(f"成功生成邮箱: {email_data.get('address')}")
                print(f"过期时间: {email_data.get('expires_at')}")
                return email_data.get('address')
            else:
                print(f"生成失败: {result.get('error')}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")
    
    return None

def main():
    print("开始测试History功能修复...")
    
    # 1. 获取数据库中所有的session_id
    print("\n1. 获取数据库中的所有session_id:")
    sessions = get_all_sessions_from_db()
    for session in sessions:
        print(f"  - {session}")
    
    # 2. 测试每个session的历史记录
    print("\n2. 测试每个session的历史记录:")
    for session in sessions:
        test_get_history_for_session(session)
    
    # 3. 测试使用第一个session生成新邮箱
    if sessions:
        first_session = sessions[0]
        print(f"\n3. 测试使用第一个session ({first_session}) 生成新邮箱:")
        new_email = test_generate_email_with_session(first_session, "test-fix")
        
        if new_email:
            # 4. 再次获取该session的历史记录，验证新邮箱是否被添加
            print(f"\n4. 验证新邮箱是否被添加到历史记录:")
            test_get_history_for_session(first_session)
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
