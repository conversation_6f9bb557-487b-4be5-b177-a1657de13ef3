"""
前端测试模块
测试前端相关功能，包括静态文件服务、模板渲染、前端API交互等
"""
import pytest
import os
from pathlib import Path


@pytest.mark.frontend
class TestStaticFileServing:
    """静态文件服务测试"""

    def test_index_page_loads(self, client):
        """测试主页加载"""
        response = client.get('/')
        assert response.status_code == 200
        assert response.content_type.startswith('text/html')

    def test_static_css_files(self, client):
        """测试CSS文件服务"""
        # 检查是否有CSS文件
        static_dir = Path(__file__).parent.parent / 'static'
        if static_dir.exists():
            css_files = list(static_dir.glob('**/*.css'))
            for css_file in css_files:
                relative_path = css_file.relative_to(static_dir.parent)
                response = client.get(f'/{relative_path}')
                assert response.status_code == 200
                assert 'text/css' in response.content_type

    def test_static_js_files(self, client):
        """测试JavaScript文件服务"""
        static_dir = Path(__file__).parent.parent / 'static'
        if static_dir.exists():
            js_files = list(static_dir.glob('**/*.js'))
            for js_file in js_files:
                relative_path = js_file.relative_to(static_dir.parent)
                response = client.get(f'/{relative_path}')
                assert response.status_code == 200
                assert 'javascript' in response.content_type or 'text/plain' in response.content_type

    def test_favicon_exists(self, client):
        """测试favicon是否存在"""
        response = client.get('/favicon.ico')
        # favicon可能不存在，这是正常的
        assert response.status_code in [200, 404]


@pytest.mark.frontend
class TestTemplateRendering:
    """模板渲染测试"""

    def test_index_template_content(self, client):
        """测试主页模板内容"""
        response = client.get('/')
        assert response.status_code == 200
        
        content = response.get_data(as_text=True)
        
        # 检查基本HTML结构
        assert '<!DOCTYPE html>' in content or '<html' in content
        assert '<head>' in content
        assert '<body>' in content
        
        # 检查是否包含临时邮箱相关内容
        assert any(keyword in content.lower() for keyword in [
            'email', 'temporary', 'temp', 'mail', '临时', '邮箱', '邮件'
        ])

    def test_meta_tags_present(self, client):
        """测试meta标签是否存在"""
        response = client.get('/')
        assert response.status_code == 200
        
        content = response.get_data(as_text=True)
        
        # 检查基本meta标签
        assert '<meta charset=' in content or '<meta name="charset"' in content
        assert 'viewport' in content


@pytest.mark.frontend
class TestFrontendAPIIntegration:
    """前端API集成测试"""

    def test_api_endpoints_accessible_from_frontend(self, client):
        """测试前端可以访问的API端点"""
        # 测试CORS头部（如果配置了的话）
        api_endpoints = [
            '/api/generate-address',
            '/api/emails',
            '/api/email-history'
        ]
        
        for endpoint in api_endpoints:
            # 发送OPTIONS请求测试CORS
            response = client.options(endpoint)
            # OPTIONS请求可能返回200或405，都是正常的
            assert response.status_code in [200, 405]

    def test_api_error_responses_format(self, client):
        """测试API错误响应格式"""
        # 测试无效请求的错误格式
        response = client.get('/api/emails')  # 缺少address参数
        assert response.status_code == 400
        
        result = response.get_json()
        assert 'error' in result
        assert 'success' in result
        assert result['success'] is False

    def test_api_success_responses_format(self, client):
        """测试API成功响应格式"""
        # 生成邮箱地址
        response = client.post('/api/generate-address',
                              json={},
                              content_type='application/json')
        assert response.status_code == 201
        
        result = response.get_json()
        assert 'success' in result
        assert result['success'] is True
        assert 'data' in result


@pytest.mark.frontend
class TestUserExperience:
    """用户体验测试"""

    def test_page_load_performance(self, client):
        """测试页面加载性能"""
        import time
        
        start_time = time.time()
        response = client.get('/')
        end_time = time.time()
        
        assert response.status_code == 200
        
        # 页面应该在合理时间内加载（1秒内）
        load_time = end_time - start_time
        assert load_time < 1.0, f"Page load time {load_time:.2f}s is too slow"

    def test_responsive_design_indicators(self, client):
        """测试响应式设计指标"""
        response = client.get('/')
        assert response.status_code == 200
        
        content = response.get_data(as_text=True)
        
        # 检查响应式设计相关的meta标签和CSS
        responsive_indicators = [
            'viewport',
            'device-width',
            'initial-scale',
            '@media',
            'responsive',
            'mobile'
        ]
        
        # 至少应该有viewport meta标签
        assert 'viewport' in content

    def test_accessibility_basics(self, client):
        """测试基本可访问性"""
        response = client.get('/')
        assert response.status_code == 200
        
        content = response.get_data(as_text=True)
        
        # 检查基本可访问性元素
        accessibility_indicators = [
            'alt=',  # 图片alt属性
            'title=',  # title属性
            'aria-',  # ARIA属性
            'role=',  # role属性
            '<label',  # 表单标签
        ]
        
        # 不强制要求所有指标，但检查是否有考虑可访问性
        has_accessibility = any(indicator in content for indicator in accessibility_indicators)
        # 这个测试主要是提醒，不强制失败
        if not has_accessibility:
            print("Warning: No accessibility indicators found in the page")


@pytest.mark.frontend
class TestJavaScriptIntegration:
    """JavaScript集成测试"""

    def test_javascript_api_calls_structure(self, client):
        """测试JavaScript API调用结构"""
        response = client.get('/')
        assert response.status_code == 200
        
        content = response.get_data(as_text=True)
        
        # 检查是否包含API调用相关的JavaScript
        js_indicators = [
            'fetch(',
            'XMLHttpRequest',
            'api/',
            'json',
            'POST',
            'GET'
        ]
        
        # 检查是否有JavaScript API交互代码
        has_api_js = any(indicator in content for indicator in js_indicators)
        
        # 如果页面包含JavaScript，应该有API交互
        if '<script' in content and 'src=' not in content:  # 内联脚本
            assert has_api_js, "Page contains JavaScript but no API interaction code found"

    def test_error_handling_in_frontend(self, client):
        """测试前端错误处理"""
        response = client.get('/')
        assert response.status_code == 200
        
        content = response.get_data(as_text=True)
        
        # 检查是否有错误处理相关的代码
        error_handling_indicators = [
            'catch(',
            'error',
            'try {',
            'onerror',
            'addEventListener'
        ]
        
        # 如果有JavaScript，应该有某种错误处理
        if '<script' in content:
            has_error_handling = any(indicator in content for indicator in error_handling_indicators)
            # 这是一个建议性检查
            if not has_error_handling:
                print("Warning: No error handling indicators found in JavaScript")


@pytest.mark.frontend
class TestSecurityHeaders:
    """安全头部测试"""

    def test_security_headers_present(self, client):
        """测试安全相关的HTTP头部"""
        response = client.get('/')
        assert response.status_code == 200
        
        # 检查推荐的安全头部
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': ['DENY', 'SAMEORIGIN'],
            'X-XSS-Protection': '1; mode=block',
        }
        
        for header, expected_values in security_headers.items():
            if header in response.headers:
                if isinstance(expected_values, list):
                    assert response.headers[header] in expected_values
                else:
                    assert response.headers[header] == expected_values

    def test_content_security_policy(self, client):
        """测试内容安全策略"""
        response = client.get('/')
        assert response.status_code == 200
        
        # CSP头部是可选的，但如果存在应该是有效的
        if 'Content-Security-Policy' in response.headers:
            csp = response.headers['Content-Security-Policy']
            assert len(csp) > 0
            # 基本检查CSP格式
            assert any(directive in csp for directive in [
                'default-src', 'script-src', 'style-src', 'img-src'
            ])


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
