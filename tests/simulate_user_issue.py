#!/usr/bin/env python3
"""
模拟用户报告的问题：
1. 生成第一个邮箱：<EMAIL>
2. 生成第二个邮箱：<EMAIL>  
3. 点击History按钮查看历史记录
4. 验证是否只显示最新的邮箱（这是bug）还是显示所有邮箱（修复后的期望行为）
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def simulate_user_workflow():
    print("=== 模拟用户工作流程 ===")
    
    # 模拟用户的session_id（使用localStorage持久化）
    session_id = "user_test_session_" + str(int(__import__('time').time()))
    print(f"使用session_id: {session_id}")
    
    # 1. 生成第一个邮箱（模拟用户输入5d483d7d0e54）
    print("\n1. 生成第一个邮箱...")
    email1 = generate_email(session_id, "5d483d7d0e54")
    if email1:
        print(f"✓ 第一个邮箱生成成功: {email1}")
    else:
        print("✗ 第一个邮箱生成失败")
        return
    
    # 2. 生成第二个邮箱（模拟用户输入Halloworld-2019）
    print("\n2. 生成第二个邮箱...")
    email2 = generate_email(session_id, "Halloworld-2019")
    if email2:
        print(f"✓ 第二个邮箱生成成功: {email2}")
    else:
        print("✗ 第二个邮箱生成失败")
        return
    
    # 3. 获取历史记录（模拟点击History按钮）
    print("\n3. 获取历史记录...")
    history = get_history(session_id)
    
    if history is not None:
        print(f"✓ 历史记录获取成功，共 {len(history)} 个邮箱:")
        for i, item in enumerate(history, 1):
            status = "活跃" if item.get("is_active") else "非活跃"
            print(f"  {i}. {item.get('email_address')} - {status}")
        
        # 4. 验证修复效果
        print("\n4. 验证修复效果...")
        expected_emails = [email1, email2]
        actual_emails = [item.get('email_address') for item in history]
        
        all_emails_present = all(email in actual_emails for email in expected_emails)
        
        if all_emails_present:
            print("✓ 修复成功！所有生成的邮箱都在历史记录中显示")
            print("✓ Bug已修复：History功能现在显示所有邮箱，而不是只显示最新的")
        else:
            print("✗ 修复失败！某些邮箱未在历史记录中显示")
            print(f"期望的邮箱: {expected_emails}")
            print(f"实际的邮箱: {actual_emails}")
    else:
        print("✗ 历史记录获取失败")

def generate_email(session_id, custom_prefix):
    """生成邮箱"""
    url = f"{BASE_URL}/api/generate-address"
    data = {
        "session_id": session_id,
        "custom_prefix": custom_prefix
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 201:
            result = response.json()
            if result.get("success"):
                return result.get("data", {}).get("address")
        print(f"生成邮箱失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"生成邮箱异常: {e}")
    
    return None

def get_history(session_id):
    """获取历史记录"""
    url = f"{BASE_URL}/api/email-history"
    params = {"session_id": session_id}
    
    try:
        response = requests.get(url, params=params)
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                return result.get("data", {}).get("history", [])
        print(f"获取历史记录失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"获取历史记录异常: {e}")
    
    return None

def test_cross_session_scenario():
    """测试跨会话场景（模拟用户关闭浏览器后重新打开）"""
    print("\n=== 测试跨会话场景 ===")
    
    # 使用现有的session_id（模拟localStorage持久化）
    existing_session = "session_1748522407167_igic2h8tk"  # 这个session已经有2个邮箱
    print(f"使用现有session_id: {existing_session}")
    
    # 获取现有历史记录
    print("\n1. 获取现有历史记录...")
    history_before = get_history(existing_session)
    if history_before:
        print(f"✓ 现有历史记录: {len(history_before)} 个邮箱")
        for i, item in enumerate(history_before, 1):
            print(f"  {i}. {item.get('email_address')}")
    
    # 生成新邮箱（模拟用户重新打开浏览器后继续使用）
    print("\n2. 生成新邮箱...")
    new_email = generate_email(existing_session, "after-reopen")
    if new_email:
        print(f"✓ 新邮箱生成成功: {new_email}")
    
    # 再次获取历史记录
    print("\n3. 获取更新后的历史记录...")
    history_after = get_history(existing_session)
    if history_after:
        print(f"✓ 更新后历史记录: {len(history_after)} 个邮箱")
        for i, item in enumerate(history_after, 1):
            status = "活跃" if item.get("is_active") else "非活跃"
            print(f"  {i}. {item.get('email_address')} - {status}")
        
        # 验证历史记录是否正确累积
        if len(history_after) == len(history_before) + 1:
            print("✓ 历史记录正确累积，新邮箱已添加到现有历史中")
        else:
            print("✗ 历史记录累积异常")

if __name__ == "__main__":
    print("开始模拟用户报告的问题...")
    simulate_user_workflow()
    test_cross_session_scenario()
    print("\n测试完成！")
