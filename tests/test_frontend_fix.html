<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试History功能修复</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .test-result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { margin: 5px; padding: 10px 15px; cursor: pointer; }
        .history-item { margin: 5px 0; padding: 8px; background: #e9ecef; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>History功能修复测试</h1>
    
    <div class="test-section">
        <h2>1. Session管理测试</h2>
        <button onclick="showCurrentSession()">显示当前Session ID</button>
        <button onclick="clearSession()">清除Session</button>
        <button onclick="createNewSession()">创建新Session</button>
        <div id="session-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 邮箱生成测试</h2>
        <button onclick="generateEmail('test-email-1')">生成邮箱1 (test-email-1)</button>
        <button onclick="generateEmail('test-email-2')">生成邮箱2 (test-email-2)</button>
        <button onclick="generateEmail()">生成随机邮箱</button>
        <div id="generate-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 历史记录测试</h2>
        <button onclick="getHistory()">获取历史记录</button>
        <button onclick="testHistoryAfterRefresh()">模拟页面刷新后获取历史</button>
        <div id="history-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 综合测试</h2>
        <button onclick="runFullTest()">运行完整测试流程</button>
        <div id="full-test-result" class="test-result"></div>
    </div>

    <script>
        const BASE_URL = 'http://127.0.0.1:5000';
        const SESSION_STORAGE_KEY = 'temp_email_session_id';
        
        // 模拟前端的session管理函数
        function getOrCreateSessionId() {
            let storedSessionId = localStorage.getItem(SESSION_STORAGE_KEY);
            
            if (!storedSessionId) {
                storedSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                localStorage.setItem(SESSION_STORAGE_KEY, storedSessionId);
                console.log('生成新的会话ID:', storedSessionId);
            } else {
                console.log('使用现有会话ID:', storedSessionId);
            }
            
            return storedSessionId;
        }
        
        function showCurrentSession() {
            const sessionId = getOrCreateSessionId();
            document.getElementById('session-result').innerHTML = 
                `<div class="success">当前Session ID: ${sessionId}</div>`;
        }
        
        function clearSession() {
            localStorage.removeItem(SESSION_STORAGE_KEY);
            document.getElementById('session-result').innerHTML = 
                `<div class="success">Session已清除</div>`;
        }
        
        function createNewSession() {
            localStorage.removeItem(SESSION_STORAGE_KEY);
            const newSessionId = getOrCreateSessionId();
            document.getElementById('session-result').innerHTML = 
                `<div class="success">新Session ID: ${newSessionId}</div>`;
        }
        
        async function generateEmail(customPrefix = null) {
            const sessionId = getOrCreateSessionId();
            const requestBody = { session_id: sessionId };
            
            if (customPrefix) {
                requestBody.custom_prefix = customPrefix;
            }
            
            try {
                const response = await fetch(`${BASE_URL}/api/generate-address`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestBody)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('generate-result').innerHTML = 
                        `<div class="success">成功生成邮箱: ${result.data.address}<br>Session: ${sessionId}</div>`;
                } else {
                    document.getElementById('generate-result').innerHTML = 
                        `<div class="error">生成失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('generate-result').innerHTML = 
                    `<div class="error">请求失败: ${error.message}</div>`;
            }
        }
        
        async function getHistory() {
            const sessionId = getOrCreateSessionId();
            
            try {
                const response = await fetch(`${BASE_URL}/api/email-history?session_id=${sessionId}`);
                const result = await response.json();
                
                if (result.success) {
                    const history = result.data.history || [];
                    let historyHtml = `<div class="success">找到 ${history.length} 个历史邮箱 (Session: ${sessionId}):</div>`;
                    
                    history.forEach((item, index) => {
                        const status = item.is_active ? '活跃' : '非活跃';
                        const expired = item.is_expired ? '已过期' : '未过期';
                        const exists = item.exists_in_db ? '存在' : '已删除';
                        const style = item.is_expired || !item.exists_in_db ? 'text-decoration: line-through; opacity: 0.6;' : '';
                        
                        historyHtml += `<div class="history-item" style="${style}">
                            ${index + 1}. ${item.email_address} - ${status}, ${expired}, ${exists}
                        </div>`;
                    });
                    
                    document.getElementById('history-result').innerHTML = historyHtml;
                } else {
                    document.getElementById('history-result').innerHTML = 
                        `<div class="error">获取历史记录失败: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('history-result').innerHTML = 
                    `<div class="error">请求失败: ${error.message}</div>`;
            }
        }
        
        function testHistoryAfterRefresh() {
            // 模拟页面刷新：重新获取session ID（应该从localStorage读取现有的）
            const sessionId = getOrCreateSessionId();
            document.getElementById('history-result').innerHTML = 
                `<div>模拟页面刷新后，Session ID: ${sessionId}</div>`;
            
            // 然后获取历史记录
            setTimeout(getHistory, 500);
        }
        
        async function runFullTest() {
            const resultDiv = document.getElementById('full-test-result');
            resultDiv.innerHTML = '<div>开始运行完整测试...</div>';
            
            try {
                // 1. 清除现有session
                localStorage.removeItem(SESSION_STORAGE_KEY);
                resultDiv.innerHTML += '<div>✓ 1. 清除现有session</div>';
                
                // 2. 生成第一个邮箱
                await generateEmailForTest('test-full-1');
                resultDiv.innerHTML += '<div>✓ 2. 生成第一个邮箱</div>';
                
                // 3. 生成第二个邮箱
                await generateEmailForTest('test-full-2');
                resultDiv.innerHTML += '<div>✓ 3. 生成第二个邮箱</div>';
                
                // 4. 获取历史记录
                const history1 = await getHistoryForTest();
                resultDiv.innerHTML += `<div>✓ 4. 获取历史记录: ${history1.length} 个邮箱</div>`;
                
                // 5. 模拟页面刷新（重新获取session）
                const sessionBefore = getOrCreateSessionId();
                const sessionAfter = getOrCreateSessionId();
                const sessionPersisted = sessionBefore === sessionAfter;
                resultDiv.innerHTML += `<div>✓ 5. 模拟页面刷新: Session持久化 ${sessionPersisted ? '成功' : '失败'}</div>`;
                
                // 6. 再次获取历史记录
                const history2 = await getHistoryForTest();
                resultDiv.innerHTML += `<div>✓ 6. 刷新后获取历史记录: ${history2.length} 个邮箱</div>`;
                
                // 7. 验证结果
                const testPassed = history1.length === history2.length && history1.length >= 2 && sessionPersisted;
                resultDiv.innerHTML += `<div class="${testPassed ? 'success' : 'error'}">
                    测试结果: ${testPassed ? '通过' : '失败'}
                    <br>期望: 历史记录在页面刷新后保持一致，且包含多个邮箱
                    <br>实际: 刷新前${history1.length}个，刷新后${history2.length}个，Session持久化${sessionPersisted}
                </div>`;
                
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">测试过程中出错: ${error.message}</div>`;
            }
        }
        
        async function generateEmailForTest(prefix) {
            const sessionId = getOrCreateSessionId();
            const response = await fetch(`${BASE_URL}/api/generate-address`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ session_id: sessionId, custom_prefix: prefix })
            });
            return await response.json();
        }
        
        async function getHistoryForTest() {
            const sessionId = getOrCreateSessionId();
            const response = await fetch(`${BASE_URL}/api/email-history?session_id=${sessionId}`);
            const result = await response.json();
            return result.success ? (result.data.history || []) : [];
        }
        
        // 页面加载时显示当前session
        window.onload = function() {
            showCurrentSession();
        };
    </script>
</body>
</html>
